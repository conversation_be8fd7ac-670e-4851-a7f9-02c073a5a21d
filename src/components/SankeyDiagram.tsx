import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { sankey, sankeyLinkHorizontal, SankeyNode, SankeyLink } from 'd3-sankey';
import { MoneyFlowTransaction } from '@/services/moneyFlowService';

interface SankeyNodeData {
  id: string;
  name: string;
  category: 'source' | 'center' | 'target';
  value: number;
  color: string;
}

interface SankeyLinkData {
  source: string;
  target: string;
  value: number;
  color: string;
  transactions: MoneyFlowTransaction[];
}

interface SankeyDiagramProps {
  nodes: SankeyNodeData[];
  links: SankeyLinkData[];
  width?: number;
  height?: number;
  onLinkClick?: (link: SankeyLinkData) => void;
  onNodeClick?: (node: SankeyNodeData) => void;
}

const SankeyDiagram: React.FC<SankeyDiagramProps> = ({
  nodes,
  links,
  width = 800,
  height = 400,
  onLinkClick,
  onNodeClick
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [hoveredLink, setHoveredLink] = useState<SankeyLinkData | null>(null);
  const [hoveredNode, setHoveredNode] = useState<SankeyNodeData | null>(null);
  const [tooltip, setTooltip] = useState<{
    x: number;
    y: number;
    content: string;
    visible: boolean;
  }>({ x: 0, y: 0, content: '', visible: false });

  useEffect(() => {
    if (!svgRef.current || nodes.length === 0 || links.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 20, bottom: 20, left: 20 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Create sankey generator
    const sankeyGenerator = sankey<SankeyNodeData, SankeyLinkData>()
      .nodeWidth(15)
      .nodePadding(10)
      .extent([[1, 1], [innerWidth - 1, innerHeight - 5]]);

    // Prepare data for sankey - d3-sankey requires node indices, not string IDs
    const nodeMap = new Map(nodes.map((node, index) => [node.id, index]));

    // Filter out links that don't have matching nodes and convert to indices
    const validLinks = links
      .filter(link => nodeMap.has(link.source) && nodeMap.has(link.target))
      .map(link => ({
        ...link,
        source: nodeMap.get(link.source)!,
        target: nodeMap.get(link.target)!
      }));

    const sankeyData = {
      nodes: nodes.map(d => ({ ...d })),
      links: validLinks
    };

    // Generate sankey layout
    const { nodes: sankeyNodes, links: sankeyLinks } = sankeyGenerator(sankeyData);

    // Create gradient definitions
    const defs = svg.append('defs');

    sankeyLinks.forEach((link, i) => {
      const gradient = defs
        .append('linearGradient')
        .attr('id', `gradient-${i}`)
        .attr('gradientUnits', 'userSpaceOnUse')
        .attr('x1', (link.source as any).x1)
        .attr('x2', (link.target as any).x0);

      gradient
        .append('stop')
        .attr('offset', '0%')
        .attr('stop-color', (link.source as any).color || '#3B82F6')
        .attr('stop-opacity', 0.8);

      gradient
        .append('stop')
        .attr('offset', '100%')
        .attr('stop-color', (link.target as any).color || '#8B5CF6')
        .attr('stop-opacity', 0.8);
    });

    // Draw links
    const linkElements = g
      .append('g')
      .selectAll('.link')
      .data(sankeyLinks)
      .enter()
      .append('path')
      .attr('class', 'link')
      .attr('d', sankeyLinkHorizontal())
      .attr('stroke', (d, i) => `url(#gradient-${i})`)
      .attr('stroke-width', (d: any) => Math.max(1, d.width))
      .attr('fill', 'none')
      .attr('opacity', 0.7)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d: any) {
        d3.select(this).attr('opacity', 1);
        setHoveredLink(d);

        const rect = svgRef.current?.getBoundingClientRect();
        if (rect) {
          setTooltip({
            x: event.clientX - rect.left,
            y: event.clientY - rect.top,
            content: `${formatCurrency(d.value)} (${d.transactions.length} txns)`,
            visible: true
          });
        }
      })
      .on('mouseout', function(event, d: any) {
        d3.select(this).attr('opacity', 0.7);
        setHoveredLink(null);
        setTooltip(prev => ({ ...prev, visible: false }));
      })
      .on('click', function(event, d: any) {
        // Find the original link data by matching source/target indices
        const originalLink = links.find(link => {
          const sourceIndex = nodeMap.get(link.source);
          const targetIndex = nodeMap.get(link.target);
          return sourceIndex === (d.source as any).index && targetIndex === (d.target as any).index;
        });
        onLinkClick?.(originalLink || d);
      });

    // Draw nodes
    const nodeElements = g
      .append('g')
      .selectAll('.node')
      .data(sankeyNodes)
      .enter()
      .append('g')
      .attr('class', 'node')
      .attr('transform', (d: any) => `translate(${d.x0},${d.y0})`);

    // Node rectangles
    nodeElements
      .append('rect')
      .attr('height', (d: any) => d.y1 - d.y0)
      .attr('width', sankeyGenerator.nodeWidth())
      .attr('fill', (d: any) => d.color)
      .attr('stroke', '#000')
      .attr('stroke-width', 0.5)
      .attr('rx', 3)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d: any) {
        setHoveredNode(d);

        const rect = svgRef.current?.getBoundingClientRect();
        if (rect) {
          setTooltip({
            x: event.clientX - rect.left,
            y: event.clientY - rect.top,
            content: `${d.name}\n${formatCurrency(d.value)}`,
            visible: true
          });
        }
      })
      .on('mouseout', function(event, d: any) {
        setHoveredNode(null);
        setTooltip(prev => ({ ...prev, visible: false }));
      })
      .on('click', function(event, d: any) {
        // Find the original node data
        const originalNode = nodes[d.index] || d;
        onNodeClick?.(originalNode);
      });

    // Node labels
    nodeElements
      .append('text')
      .attr('x', (d: any) => d.x0 < innerWidth / 2 ? sankeyGenerator.nodeWidth() + 6 : -6)
      .attr('y', (d: any) => (d.y1 + d.y0) / 2)
      .attr('dy', '0.35em')
      .attr('text-anchor', (d: any) => d.x0 < innerWidth / 2 ? 'start' : 'end')
      .text((d: any) => d.name)
      .attr('font-family', 'sans-serif')
      .attr('font-size', '12px')
      .attr('fill', '#ffffff')
      .attr('font-weight', '500');

    // Add value labels on nodes
    nodeElements
      .append('text')
      .attr('x', (d: any) => d.x0 < innerWidth / 2 ? sankeyGenerator.nodeWidth() + 6 : -6)
      .attr('y', (d: any) => (d.y1 + d.y0) / 2 + 15)
      .attr('dy', '0.35em')
      .attr('text-anchor', (d: any) => d.x0 < innerWidth / 2 ? 'start' : 'end')
      .text((d: any) => formatCurrency(d.value))
      .attr('font-family', 'sans-serif')
      .attr('font-size', '10px')
      .attr('fill', '#9CA3AF')
      .attr('font-weight', '400');

  }, [nodes, links, width, height, onLinkClick, onNodeClick]);

  const formatCurrency = (value: number): string => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  return (
    <div className="relative">
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="bg-background-secondary rounded-lg border border-border"
      />

      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="absolute z-10 px-3 py-2 text-xs font-medium text-white bg-gray-900 rounded-lg shadow-lg pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            transform: 'translateY(-100%)'
          }}
        >
          <div className="whitespace-pre-line">{tooltip.content}</div>
        </div>
      )}

      {/* Legend */}
      <div className="absolute top-4 right-4 bg-background-secondary/90 backdrop-blur-sm rounded-lg p-3 border border-border">
        <div className="text-xs font-semibold text-foreground mb-2">Flow Direction</div>
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-blue-500"></div>
            <span className="text-xs text-foreground-muted">Inbound</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-green-500"></div>
            <span className="text-xs text-foreground-muted">Center</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-purple-500"></div>
            <span className="text-xs text-foreground-muted">Outbound</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SankeyDiagram;
