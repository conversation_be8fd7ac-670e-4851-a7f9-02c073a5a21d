import React, { useState, useEffect, useRef } from 'react';
import {
  FaWater, FaEthereum, FaCoins, FaArrowDown, FaArrowUp,
  FaExchangeAlt, FaChartArea, FaList, FaFilter, FaDownload,
  FaS<PERSON>ner, FaExclamationTriangle
} from 'react-icons/fa';
import SankeyDiagram from './SankeyDiagram';
import MoneyFlowFilters from './MoneyFlowFilters';
import TransactionFlowDetails from './TransactionFlowDetails';
import {
  moneyFlowService,
  MoneyFlowData,
  MoneyFlowFilters as IMoneyFlowFilters,
  MoneyFlowTransaction,
  MoneyFlowAccount
} from '@/services/moneyFlowService';

interface MoneyFlowTracingProps {
  walletAddress: string;
  className?: string;
}

const MoneyFlowTracing: React.FC<MoneyFlowTracingProps> = ({
  walletAddress,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'eth' | 'token'>('eth');
  const [viewMode, setViewMode] = useState<'sankey' | 'list'>('sankey');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [moneyFlowData, setMoneyFlowData] = useState<MoneyFlowData | null>(null);
  const [selectedTransactions, setSelectedTransactions] = useState<MoneyFlowTransaction[]>([]);
  const [showTransactionDetails, setShowTransactionDetails] = useState(false);
  const [sankeyContainerRef, setSankeyContainerRef] = useState<HTMLDivElement | null>(null);
  const [sankeyDimensions, setSankeyDimensions] = useState({ width: 800, height: 500 });
  const [filters, setFilters] = useState<IMoneyFlowFilters>({
    flowType: 'both',
    transferType: 'eth',
    topN: 50,
    riskLevel: 'all'
  });

  useEffect(() => {
    loadMoneyFlowData();
  }, [walletAddress, activeTab, filters]);

  // Calculate responsive dimensions for Sankey diagram
  useEffect(() => {
    const updateDimensions = () => {
      if (sankeyContainerRef) {
        const containerRect = sankeyContainerRef.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = Math.max(400, Math.min(600, containerWidth * 0.6)); // Aspect ratio 5:3

        setSankeyDimensions({
          width: Math.max(400, containerWidth - 48), // Account for padding
          height: containerHeight
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => window.removeEventListener('resize', updateDimensions);
  }, [sankeyContainerRef]);

  // Update dimensions when container ref changes
  useEffect(() => {
    if (sankeyContainerRef) {
      const updateDimensions = () => {
        const containerRect = sankeyContainerRef.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = Math.max(400, Math.min(600, containerWidth * 0.6));

        setSankeyDimensions({
          width: Math.max(400, containerWidth - 48),
          height: containerHeight
        });
      };

      // Use ResizeObserver for better performance
      const resizeObserver = new ResizeObserver(updateDimensions);
      resizeObserver.observe(sankeyContainerRef);

      return () => resizeObserver.disconnect();
    }
  }, [sankeyContainerRef]);

  const loadMoneyFlowData = async () => {
    if (!walletAddress) return;

    setLoading(true);
    setError(null);

    try {
      const updatedFilters = {
        ...filters,
        transferType: activeTab
      };

      const data = await moneyFlowService.getMoneyFlowData(walletAddress, updatedFilters);
      setMoneyFlowData(data);
    } catch (err) {
      setError('Failed to load money flow data');
      console.error('Error loading money flow data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: IMoneyFlowFilters) => {
    setFilters(newFilters);
  };

  const handleSankeyLinkClick = (linkData: any) => {
    setSelectedTransactions(linkData.transactions || []);
    setShowTransactionDetails(true);
  };

  const handleSankeyNodeClick = (nodeData: any) => {
    // Filter transactions for this specific node
    if (moneyFlowData) {
      const nodeTransactions = moneyFlowData.transactions.filter(tx =>
        tx.from === nodeData.id || tx.to === nodeData.id
      );
      setSelectedTransactions(nodeTransactions);
      setShowTransactionDetails(true);
    }
  };

  const formatCurrency = (value: number): string => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    } else {
      return `$${value.toFixed(2)}`;
    }
  };

  const formatAddress = (address: string): string => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const getAccountTypeIcon = (account: MoneyFlowAccount) => {
    if (account.isExchange) return <FaExchangeAlt className="text-blue-400" />;
    if (account.isContract) return <FaCoins className="text-purple-400" />;
    return <FaWater className="text-gray-400" />;
  };

  const getAccountTypeColor = (account: MoneyFlowAccount): string => {
    if (account.isExchange) return 'border-blue-400/30 bg-blue-400/10';
    if (account.isContract) return 'border-purple-400/30 bg-purple-400/10';
    if (account.riskScore > 70) return 'border-red-400/30 bg-red-400/10';
    return 'border-gray-400/30 bg-gray-400/10';
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="flex items-center gap-3">
          <FaSpinner className="animate-spin text-accent-400" />
          <span className="text-foreground-muted">Loading money flow data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="flex items-center gap-3 text-red-400">
          <FaExclamationTriangle />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-500">
            <FaWater className="text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-foreground">Money Flow Tracing</h3>
            <p className="text-sm text-foreground-muted">
              Analyze {activeTab.toUpperCase()} transfers for {formatAddress(walletAddress)}
            </p>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <div className="flex gap-1 p-1 rounded-lg bg-background-tertiary">
            <button
              onClick={() => setViewMode('sankey')}
              className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                viewMode === 'sankey'
                  ? 'bg-accent-400 text-white'
                  : 'text-foreground-muted hover:text-foreground'
              }`}
            >
              <FaChartArea size={12} />
              Sankey
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                viewMode === 'list'
                  ? 'bg-accent-400 text-white'
                  : 'text-foreground-muted hover:text-foreground'
              }`}
            >
              <FaList size={12} />
              List
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex gap-1 p-1 rounded-lg bg-background-tertiary">
        <button
          onClick={() => setActiveTab('eth')}
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg text-sm font-semibold transition-all duration-300 ${
            activeTab === 'eth'
              ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
              : 'text-foreground-muted hover:text-foreground hover:bg-background-secondary'
          }`}
        >
          <FaEthereum size={14} />
          ETH Transfers
        </button>
        <button
          onClick={() => setActiveTab('token')}
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg text-sm font-semibold transition-all duration-300 ${
            activeTab === 'token'
              ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
              : 'text-foreground-muted hover:text-foreground hover:bg-background-secondary'
          }`}
        >
          <FaCoins size={14} />
          Token Transfers
        </button>
      </div>

      {/* Filters */}
      <MoneyFlowFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
        availableTokens={moneyFlowData?.summary.topTokens.map(t => t.symbol) || []}
      />

      {/* Summary Stats */}
      {moneyFlowData && (
        <div className="flex flex-col gap-4">
          <div className="p-3 border rounded-lg bg-background-secondary lg:p-4 border-border">
            <div className="flex flex-col items-center mb-3 text-center">
              <div className="flex items-center justify-center w-8 h-8 mb-2 rounded-full lg:w-10 lg:h-10 bg-green-500/20">
                <FaArrowDown className="text-sm text-green-400 lg:text-base" />
              </div>
              <span className="text-xs font-medium text-foreground-muted">Total Inbound</span>
            </div>
            <div className="text-center">
              <div className="text-sm font-bold lg:text-base text-foreground">
                {formatCurrency(moneyFlowData.summary.totalInboundUsd)}
              </div>
              <div className="mt-1 text-xs text-foreground-muted">
                {moneyFlowData.summary.totalInbound.toFixed(2)} ETH
              </div>
            </div>
          </div>
          <div className="p-3 border rounded-lg bg-background-secondary lg:p-4 border-border">
            <div className="flex flex-col items-center mb-3 text-center">
              <div className="flex items-center justify-center w-8 h-8 mb-2 rounded-full lg:w-10 lg:h-10 bg-red-500/20">
                <FaArrowUp className="text-sm text-red-400 lg:text-base" />
              </div>
              <span className="text-xs font-medium text-foreground-muted">Total Outbound</span>
            </div>
            <div className="text-center">
              <div className="text-sm font-bold lg:text-base text-foreground">
                {formatCurrency(moneyFlowData.summary.totalOutboundUsd)}
              </div>
              <div className="mt-1 text-xs text-foreground-muted">
                {moneyFlowData.summary.totalOutbound.toFixed(2)} ETH
              </div>
            </div>
          </div>
          <div className="p-3 border rounded-lg bg-background-secondary lg:p-4 border-border">
            <div className="flex flex-col items-center mb-3 text-center">
              <div className="flex items-center justify-center w-8 h-8 mb-2 rounded-full lg:w-10 lg:h-10 bg-blue-500/20">
                <FaExchangeAlt className="text-sm text-blue-400 lg:text-base" />
              </div>
              <span className="text-xs font-medium text-foreground-muted">Counterparties</span>
            </div>
            <div className="text-center">
              <div className="text-sm font-bold lg:text-base text-foreground">
                {moneyFlowData.summary.uniqueCounterparties}
              </div>
              <div className="mt-1 text-xs text-foreground-muted">
                Unique addresses
              </div>
            </div>
          </div>
          <div className="p-3 border rounded-lg bg-background-secondary lg:p-4 border-border">
            <div className="flex flex-col items-center mb-3 text-center">
              <div className="flex items-center justify-center w-8 h-8 mb-2 rounded-full lg:w-10 lg:h-10 bg-yellow-500/20">
                <FaCoins className="text-sm text-yellow-400 lg:text-base" />
              </div>
              <span className="text-xs font-medium text-foreground-muted">Top Token</span>
            </div>
            <div className="text-center">
              <div className="text-sm font-bold lg:text-base text-foreground">
                {moneyFlowData.summary.topTokens[0]?.symbol || 'N/A'}
              </div>
              <div className="mt-1 text-xs text-foreground-muted">
                {moneyFlowData.summary.topTokens[0] ?
                  formatCurrency(moneyFlowData.summary.topTokens[0].usdVolume) :
                  'No data'
                }
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {moneyFlowData && (
        <div className="border rounded-lg bg-background-secondary border-border">
          {viewMode === 'sankey' ? (
            <div
              ref={setSankeyContainerRef}
              className="p-4 overflow-hidden lg:p-6"
            >
              <div className="flex justify-center w-full">
                <SankeyDiagram
                  nodes={moneyFlowData.sankeyData.nodes}
                  links={moneyFlowData.sankeyData.links}
                  width={sankeyDimensions.width}
                  height={sankeyDimensions.height}
                  onLinkClick={handleSankeyLinkClick}
                  onNodeClick={handleSankeyNodeClick}
                />
              </div>
            </div>
          ) : (
            <div className="p-4 lg:p-6">
              {/* Account Lists */}
              <div className="grid max-w-full grid-cols-1 gap-4 overflow-hidden xl:grid-cols-2 lg:gap-6">
                {/* Inbound Accounts */}
                {(filters.flowType === 'inbound' || filters.flowType === 'both') && (
                  <div className="min-w-0">
                    <h4 className="mb-3 text-base font-semibold truncate lg:text-lg text-foreground lg:mb-4">
                      Top Inbound Accounts ({moneyFlowData.inboundAccounts.length})
                    </h4>
                    <div className="space-y-2 lg:space-y-3">
                      {moneyFlowData.inboundAccounts.slice(0, 10).map((account, index) => (
                        <div
                          key={account.address}
                          className={`p-2 lg:p-3 rounded-lg border cursor-pointer hover:border-accent-400/50 transition-colors ${getAccountTypeColor(account)}`}
                          onClick={() => {
                            const accountTxs = moneyFlowData.transactions.filter(tx =>
                              tx.from === account.address || tx.to === account.address
                            );
                            setSelectedTransactions(accountTxs);
                            setShowTransactionDetails(true);
                          }}
                        >
                          <div className="flex items-center justify-between min-w-0 gap-3">
                            <div className="flex items-center flex-1 min-w-0 gap-3">
                              <div className="flex items-center justify-center flex-shrink-0 w-6 h-6 rounded-lg lg:w-8 lg:h-8 bg-background-tertiary">
                                {getAccountTypeIcon(account)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium truncate text-foreground">
                                  {account.label || formatAddress(account.address)}
                                </div>
                                <div className="text-sm truncate text-foreground-muted">
                                  {account.transactionCount} transactions
                                </div>
                              </div>
                            </div>
                            <div className="flex-shrink-0 text-right">
                              <div className="text-sm font-semibold text-foreground">
                                {formatCurrency(account.totalUsdValue)}
                              </div>
                              <div className="text-xs text-foreground-muted">
                                #{index + 1}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Outbound Accounts */}
                {(filters.flowType === 'outbound' || filters.flowType === 'both') && (
                  <div className="min-w-0">
                    <h4 className="mb-3 text-base font-semibold truncate lg:text-lg text-foreground lg:mb-4">
                      Top Outbound Accounts ({moneyFlowData.outboundAccounts.length})
                    </h4>
                    <div className="space-y-2 lg:space-y-3">
                      {moneyFlowData.outboundAccounts.slice(0, 10).map((account, index) => (
                        <div
                          key={account.address}
                          className={`p-2 lg:p-3 rounded-lg border cursor-pointer hover:border-accent-400/50 transition-colors ${getAccountTypeColor(account)}`}
                          onClick={() => {
                            const accountTxs = moneyFlowData.transactions.filter(tx =>
                              tx.from === account.address || tx.to === account.address
                            );
                            setSelectedTransactions(accountTxs);
                            setShowTransactionDetails(true);
                          }}
                        >
                          <div className="flex items-center justify-between min-w-0 gap-3">
                            <div className="flex items-center flex-1 min-w-0 gap-3">
                              <div className="flex items-center justify-center flex-shrink-0 w-6 h-6 rounded-lg lg:w-8 lg:h-8 bg-background-tertiary">
                                {getAccountTypeIcon(account)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium truncate text-foreground">
                                  {account.label || formatAddress(account.address)}
                                </div>
                                <div className="text-sm truncate text-foreground-muted">
                                  {account.transactionCount} transactions
                                </div>
                              </div>
                            </div>
                            <div className="flex-shrink-0 text-right">
                              <div className="text-sm font-semibold text-foreground">
                                {formatCurrency(account.totalUsdValue)}
                              </div>
                              <div className="text-xs text-foreground-muted">
                                #{index + 1}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Transaction Details Modal */}
      <TransactionFlowDetails
        transactions={selectedTransactions}
        isOpen={showTransactionDetails}
        onClose={() => setShowTransactionDetails(false)}
        title="Flow Transaction Details"
      />
    </div>
  );
};

export default MoneyFlowTracing;
