import React, { useState } from 'react';
import MoneyFlowTracing from '../components/MoneyFlowTracing';
import SankeyDiagram from '../components/SankeyDiagram';
import MoneyFlowFilters from '../components/MoneyFlowFilters';
import TransactionFlowDetails from '../components/TransactionFlowDetails';
import { MoneyFlowFilters as IMoneyFlowFilters, MoneyFlowTransaction } from '../services/moneyFlowService';

// Test component để kiểm tra các Money Flow components
const MoneyFlowTest: React.FC = () => {
  const [showTransactionDetails, setShowTransactionDetails] = useState(false);
  const [filters, setFilters] = useState<IMoneyFlowFilters>({
    flowType: 'both',
    transferType: 'eth',
    topN: 50,
    riskLevel: 'all'
  });

  // Mock data for testing
  const mockTransactions: MoneyFlowTransaction[] = [
    {
      id: 'tx1',
      hash: '******************************************90abcdef1234567890abcdef',
      from: '******************************************',
      to: '******************************************',
      value: 1.5,
      tokenSymbol: 'ETH',
      usdValue: 3000,
      timestamp: new Date().toISOString(),
      blockNumber: 18500000,
      gasUsed: 21000,
      gasPrice: 20,
      gasFee: 0.0042,
      method: 'transfer',
      transactionType: 'transfer',
      riskLevel: 'low'
    },
    {
      id: 'tx2',
      hash: '******************************************87654321fedcba0987654321',
      from: '******************************************',
      to: '******************************************',
      value: 1000,
      token: 'USDC',
      tokenSymbol: 'USDC',
      tokenDecimals: 6,
      usdValue: 1000,
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      blockNumber: 18499999,
      gasUsed: 65000,
      gasPrice: 25,
      gasFee: 0.01625,
      method: 'transfer',
      transactionType: 'transfer',
      riskLevel: 'medium'
    }
  ];

  const mockSankeyNodes = [
    {
      id: 'source1',
      name: 'Binance',
      category: 'source' as const,
      value: 50000,
      color: '#3B82F6'
    },
    {
      id: 'center',
      name: '0x1234...5678',
      category: 'center' as const,
      value: 100000,
      color: '#10B981'
    },
    {
      id: 'target1',
      name: 'Uniswap',
      category: 'target' as const,
      value: 30000,
      color: '#8B5CF6'
    }
  ];

  const mockSankeyLinks = [
    {
      source: 'source1',
      target: 'center',
      value: 50000,
      color: '#10B981',
      transactions: mockTransactions
    },
    {
      source: 'center',
      target: 'target1',
      value: 30000,
      color: '#8B5CF6',
      transactions: mockTransactions
    }
  ];

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-foreground mb-4">Money Flow Components Test</h1>
          <p className="text-foreground-muted">Testing all Money Flow tracing components</p>
        </div>

        {/* Test MoneyFlowFilters */}
        <div className="bg-background-secondary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">Money Flow Filters Test</h2>
          <MoneyFlowFilters
            filters={filters}
            onFiltersChange={setFilters}
            availableTokens={['ETH', 'USDC', 'USDT', 'WETH', 'DAI']}
          />
        </div>

        {/* Test SankeyDiagram */}
        <div className="bg-background-secondary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">Sankey Diagram Test</h2>
          <div className="flex justify-center">
            <SankeyDiagram
              nodes={mockSankeyNodes}
              links={mockSankeyLinks}
              width={800}
              height={400}
              onLinkClick={(link) => {
                console.log('Link clicked:', link);
                setShowTransactionDetails(true);
              }}
              onNodeClick={(node) => {
                console.log('Node clicked:', node);
              }}
            />
          </div>
        </div>

        {/* Test MoneyFlowTracing */}
        <div className="bg-background-secondary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">Money Flow Tracing Test</h2>
          <MoneyFlowTracing
            walletAddress="******************************************"
          />
        </div>

        {/* Test Button for TransactionFlowDetails */}
        <div className="bg-background-secondary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">Transaction Flow Details Test</h2>
          <button
            onClick={() => setShowTransactionDetails(true)}
            className="px-4 py-2 bg-accent-400 text-white rounded-lg hover:bg-accent-500 transition-colors"
          >
            Show Transaction Details Modal
          </button>
        </div>

        {/* Current Filter State Display */}
        <div className="bg-background-secondary rounded-lg p-6">
          <h2 className="text-xl font-semibold text-foreground mb-4">Current Filter State</h2>
          <pre className="text-sm text-foreground-muted bg-background-tertiary p-4 rounded-lg overflow-auto">
            {JSON.stringify(filters, null, 2)}
          </pre>
        </div>
      </div>

      {/* Transaction Details Modal */}
      <TransactionFlowDetails
        transactions={mockTransactions}
        isOpen={showTransactionDetails}
        onClose={() => setShowTransactionDetails(false)}
        title="Test Transaction Details"
      />
    </div>
  );
};

export default MoneyFlowTest;
