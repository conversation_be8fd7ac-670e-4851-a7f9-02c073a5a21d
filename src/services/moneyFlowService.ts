// Money Flow Service - Handles money flow tracing and analysis
export interface MoneyFlowTransaction {
  id: string;
  hash: string;
  from: string;
  to: string;
  value: number;
  token?: string;
  tokenSymbol?: string;
  tokenDecimals?: number;
  usdValue?: number;
  timestamp: string;
  blockNumber: number;
  gasUsed: number;
  gasPrice: number;
  gasFee: number;
  method?: string;
  transactionType: 'transfer' | 'swap' | 'mint' | 'burn' | 'approve' | 'contract_call';
  riskLevel: 'low' | 'medium' | 'high';
}

export interface MoneyFlowAccount {
  address: string;
  label?: string;
  totalValue: number;
  totalUsdValue: number;
  transactionCount: number;
  firstSeen: string;
  lastSeen: string;
  riskScore: number;
  tags?: string[];
  isExchange?: boolean;
  isContract?: boolean;
}

export interface MoneyFlowSummary {
  totalInbound: number;
  totalOutbound: number;
  totalInboundUsd: number;
  totalOutboundUsd: number;
  uniqueCounterparties: number;
  timeRange: {
    start: string;
    end: string;
  };
  topTokens: Array<{
    symbol: string;
    volume: number;
    usdVolume: number;
    transactionCount: number;
  }>;
}

export interface MoneyFlowFilters {
  flowType: 'inbound' | 'outbound' | 'both';
  transferType: 'eth' | 'token' | 'both';
  topN: number;
  timeRange?: {
    start: string;
    end: string;
  };
  blockRange?: {
    start: number;
    end: number;
  };
  tokenFilter?: string;
  searchQuery?: string;
  minValue?: number;
  maxValue?: number;
  riskLevel?: 'low' | 'medium' | 'high' | 'all';
}

export interface MoneyFlowData {
  centerAccount: MoneyFlowAccount;
  inboundAccounts: MoneyFlowAccount[];
  outboundAccounts: MoneyFlowAccount[];
  transactions: MoneyFlowTransaction[];
  summary: MoneyFlowSummary;
  sankeyData: {
    nodes: Array<{
      id: string;
      name: string;
      category: 'source' | 'center' | 'target';
      value: number;
      color: string;
    }>;
    links: Array<{
      source: string;
      target: string;
      value: number;
      color: string;
      transactions: MoneyFlowTransaction[];
    }>;
  };
}

class MoneyFlowService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

  // Fetch money flow data for a specific wallet
  async getMoneyFlowData(
    walletAddress: string,
    filters: MoneyFlowFilters
  ): Promise<MoneyFlowData> {
    try {
      // In a real implementation, this would call the backend API
      // For now, we'll generate mock data
      return this.generateMockMoneyFlowData(walletAddress, filters);
    } catch (error) {
      console.error('Error fetching money flow data:', error);
      throw error;
    }
  }

  // Generate mock money flow data for development
  private generateMockMoneyFlowData(
    walletAddress: string,
    filters: MoneyFlowFilters
  ): MoneyFlowData {
    const centerAccount: MoneyFlowAccount = {
      address: walletAddress,
      label: 'Center Wallet',
      totalValue: 0,
      totalUsdValue: 0,
      transactionCount: 0,
      firstSeen: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
      lastSeen: new Date().toISOString(),
      riskScore: Math.random() * 100,
      tags: ['wallet'],
      isExchange: false,
      isContract: false
    };

    // Generate mock accounts
    const inboundAccounts = this.generateMockAccounts(filters.topN, 'inbound');
    const outboundAccounts = this.generateMockAccounts(filters.topN, 'outbound');

    // Generate mock transactions
    const transactions = this.generateMockTransactions(
      walletAddress,
      [...inboundAccounts, ...outboundAccounts],
      filters
    );

    // Calculate summary
    const summary = this.calculateSummary(transactions, filters);

    // Generate Sankey data
    const sankeyData = this.generateSankeyData(
      centerAccount,
      inboundAccounts,
      outboundAccounts,
      transactions
    );

    return {
      centerAccount,
      inboundAccounts,
      outboundAccounts,
      transactions,
      summary,
      sankeyData
    };
  }

  private generateMockAccounts(count: number, type: 'inbound' | 'outbound'): MoneyFlowAccount[] {
    const accounts: MoneyFlowAccount[] = [];
    const exchangeNames = ['Binance', 'Coinbase', 'Kraken', 'Uniswap', 'SushiSwap'];
    const contractNames = ['USDC Contract', 'WETH Contract', 'Compound', 'Aave', 'MakerDAO'];

    for (let i = 0; i < count; i++) {
      const isExchange = Math.random() > 0.7;
      const isContract = !isExchange && Math.random() > 0.8;

      let label: string | undefined;
      if (isExchange) {
        label = exchangeNames[Math.floor(Math.random() * exchangeNames.length)];
      } else if (isContract) {
        label = contractNames[Math.floor(Math.random() * contractNames.length)];
      }

      accounts.push({
        address: `0x${Math.random().toString(16).substr(2, 40)}`,
        label,
        totalValue: Math.random() * 1000,
        totalUsdValue: Math.random() * 100000,
        transactionCount: Math.floor(Math.random() * 100) + 1,
        firstSeen: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        lastSeen: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        riskScore: Math.random() * 100,
        tags: isExchange ? ['exchange'] : isContract ? ['contract'] : ['wallet'],
        isExchange,
        isContract
      });
    }

    return accounts.sort((a, b) => b.totalUsdValue - a.totalUsdValue);
  }

  private generateMockTransactions(
    centerAddress: string,
    accounts: MoneyFlowAccount[],
    filters: MoneyFlowFilters
  ): MoneyFlowTransaction[] {
    const transactions: MoneyFlowTransaction[] = [];
    const tokens = ['ETH', 'USDC', 'USDT', 'WETH', 'DAI', 'LINK', 'UNI'];
    const methods = ['transfer', 'swap', 'mint', 'burn', 'approve', 'multicall'];

    // Limit to first 20 accounts to avoid too many transactions
    const limitedAccounts = accounts.slice(0, 20);

    limitedAccounts.forEach((account, accountIndex) => {
      const txCount = Math.floor(Math.random() * 5) + 1; // Reduce transaction count

      for (let i = 0; i < txCount; i++) {
        const isInbound = Math.random() > 0.5;
        const token = filters.transferType === 'eth' ? 'ETH' :
                     filters.transferType === 'token' ? tokens[Math.floor(Math.random() * (tokens.length - 1)) + 1] :
                     tokens[Math.floor(Math.random() * tokens.length)];

        const value = Math.random() * 100;
        const usdValue = value * (token === 'ETH' ? 2000 : token === 'USDC' || token === 'USDT' ? 1 : Math.random() * 10);

        transactions.push({
          id: `tx_${accountIndex}_${i}`,
          hash: `0x${Math.random().toString(16).substr(2, 64)}`,
          from: isInbound ? account.address : centerAddress,
          to: isInbound ? centerAddress : account.address,
          value,
          token,
          tokenSymbol: token,
          tokenDecimals: token === 'USDC' || token === 'USDT' ? 6 : 18,
          usdValue,
          timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
          blockNumber: ******** + Math.floor(Math.random() * 100000),
          gasUsed: 21000 + Math.floor(Math.random() * 200000),
          gasPrice: Math.random() * 50,
          gasFee: Math.random() * 0.01,
          method: methods[Math.floor(Math.random() * methods.length)],
          transactionType: 'transfer',
          riskLevel: account.riskScore > 70 ? 'high' : account.riskScore > 40 ? 'medium' : 'low'
        });
      }
    });

    return transactions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  private calculateSummary(transactions: MoneyFlowTransaction[], filters: MoneyFlowFilters): MoneyFlowSummary {
    const inboundTxs = transactions.filter(tx => tx.to === transactions[0]?.to);
    const outboundTxs = transactions.filter(tx => tx.from === transactions[0]?.to);

    const totalInbound = inboundTxs.reduce((sum, tx) => sum + tx.value, 0);
    const totalOutbound = outboundTxs.reduce((sum, tx) => sum + tx.value, 0);
    const totalInboundUsd = inboundTxs.reduce((sum, tx) => sum + (tx.usdValue || 0), 0);
    const totalOutboundUsd = outboundTxs.reduce((sum, tx) => sum + (tx.usdValue || 0), 0);

    const uniqueCounterparties = new Set([
      ...inboundTxs.map(tx => tx.from),
      ...outboundTxs.map(tx => tx.to)
    ]).size;

    // Calculate top tokens
    const tokenStats = new Map<string, { volume: number; usdVolume: number; count: number }>();
    transactions.forEach(tx => {
      const token = tx.tokenSymbol || 'ETH';
      const current = tokenStats.get(token) || { volume: 0, usdVolume: 0, count: 0 };
      tokenStats.set(token, {
        volume: current.volume + tx.value,
        usdVolume: current.usdVolume + (tx.usdValue || 0),
        count: current.count + 1
      });
    });

    const topTokens = Array.from(tokenStats.entries())
      .map(([symbol, stats]) => ({
        symbol,
        volume: stats.volume,
        usdVolume: stats.usdVolume,
        transactionCount: stats.count
      }))
      .sort((a, b) => b.usdVolume - a.usdVolume)
      .slice(0, 10);

    const timestamps = transactions.map(tx => new Date(tx.timestamp).getTime());
    const timeRange = {
      start: new Date(Math.min(...timestamps)).toISOString(),
      end: new Date(Math.max(...timestamps)).toISOString()
    };

    return {
      totalInbound,
      totalOutbound,
      totalInboundUsd,
      totalOutboundUsd,
      uniqueCounterparties,
      timeRange,
      topTokens
    };
  }

  private generateSankeyData(
    centerAccount: MoneyFlowAccount,
    inboundAccounts: MoneyFlowAccount[],
    outboundAccounts: MoneyFlowAccount[],
    transactions: MoneyFlowTransaction[]
  ) {
    const nodes = [
      // Inbound nodes (sources)
      ...inboundAccounts.slice(0, 10).map(account => ({
        id: account.address,
        name: account.label || this.truncateAddress(account.address),
        category: 'source' as const,
        value: account.totalUsdValue,
        color: this.getAccountColor(account)
      })),
      // Center node
      {
        id: centerAccount.address,
        name: centerAccount.label || this.truncateAddress(centerAccount.address),
        category: 'center' as const,
        value: centerAccount.totalUsdValue,
        color: '#10B981'
      },
      // Outbound nodes (targets)
      ...outboundAccounts.slice(0, 10).map(account => ({
        id: account.address,
        name: account.label || this.truncateAddress(account.address),
        category: 'target' as const,
        value: account.totalUsdValue,
        color: this.getAccountColor(account)
      }))
    ];

    const links: any[] = [];

    // Create a set of valid node IDs
    const validNodeIds = new Set(nodes.map(node => node.id));

    // Group transactions by account pairs, only for valid nodes
    const linkMap = new Map<string, { value: number; transactions: MoneyFlowTransaction[] }>();

    transactions.forEach(tx => {
      // Only create links for nodes that exist in our node list
      if (validNodeIds.has(tx.from) && validNodeIds.has(tx.to)) {
        const key = `${tx.from}-${tx.to}`;
        const current = linkMap.get(key) || { value: 0, transactions: [] };
        linkMap.set(key, {
          value: current.value + (tx.usdValue || 0),
          transactions: [...current.transactions, tx]
        });
      }
    });

    linkMap.forEach((data, key) => {
      const [source, target] = key.split('-');
      // Double check that both source and target exist
      if (validNodeIds.has(source) && validNodeIds.has(target)) {
        links.push({
          source,
          target,
          value: data.value,
          color: this.getLinkColor(data.value),
          transactions: data.transactions
        });
      }
    });

    return { nodes, links };
  }

  private getAccountColor(account: MoneyFlowAccount): string {
    if (account.isExchange) return '#3B82F6'; // Blue for exchanges
    if (account.isContract) return '#8B5CF6'; // Purple for contracts
    if (account.riskScore > 70) return '#EF4444'; // Red for high risk
    return '#6B7280'; // Gray for regular wallets
  }

  private getLinkColor(value: number): string {
    if (value > 100000) return '#10B981'; // Green for high value
    if (value > 10000) return '#F59E0B'; // Amber for medium value
    return '#6B7280'; // Gray for low value
  }

  private truncateAddress(address: string): string {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  }

  // Filter transactions based on criteria
  filterTransactions(
    transactions: MoneyFlowTransaction[],
    filters: Partial<MoneyFlowFilters>
  ): MoneyFlowTransaction[] {
    return transactions.filter(tx => {
      // Search query filter
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        if (!tx.from.toLowerCase().includes(query) &&
            !tx.to.toLowerCase().includes(query) &&
            !(tx.tokenSymbol?.toLowerCase().includes(query))) {
          return false;
        }
      }

      // Token filter
      if (filters.tokenFilter && filters.tokenFilter !== 'all') {
        if (tx.tokenSymbol !== filters.tokenFilter) {
          return false;
        }
      }

      // Value range filter
      if (filters.minValue !== undefined && (tx.usdValue || 0) < filters.minValue) {
        return false;
      }
      if (filters.maxValue !== undefined && (tx.usdValue || 0) > filters.maxValue) {
        return false;
      }

      // Risk level filter
      if (filters.riskLevel && filters.riskLevel !== 'all') {
        if (tx.riskLevel !== filters.riskLevel) {
          return false;
        }
      }

      // Time range filter
      if (filters.timeRange) {
        const txTime = new Date(tx.timestamp).getTime();
        const startTime = new Date(filters.timeRange.start).getTime();
        const endTime = new Date(filters.timeRange.end).getTime();
        if (txTime < startTime || txTime > endTime) {
          return false;
        }
      }

      // Block range filter
      if (filters.blockRange) {
        if (tx.blockNumber < filters.blockRange.start || tx.blockNumber > filters.blockRange.end) {
          return false;
        }
      }

      return true;
    });
  }

  // Get transaction details by ID
  async getTransactionDetails(transactionId: string): Promise<MoneyFlowTransaction | null> {
    try {
      // In a real implementation, this would call the backend API
      // For now, return mock data
      return {
        id: transactionId,
        hash: `0x${Math.random().toString(16).substr(2, 64)}`,
        from: `0x${Math.random().toString(16).substr(2, 40)}`,
        to: `0x${Math.random().toString(16).substr(2, 40)}`,
        value: Math.random() * 100,
        tokenSymbol: 'ETH',
        usdValue: Math.random() * 200000,
        timestamp: new Date().toISOString(),
        blockNumber: ******** + Math.floor(Math.random() * 100000),
        gasUsed: 21000,
        gasPrice: 20,
        gasFee: 0.0042,
        method: 'transfer',
        transactionType: 'transfer',
        riskLevel: 'low'
      };
    } catch (error) {
      console.error('Error fetching transaction details:', error);
      return null;
    }
  }
}

// Export singleton instance
export const moneyFlowService = new MoneyFlowService();
export default moneyFlowService;
